{"name": "mepond-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/geolocation": "^3.1.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/elements": "^2.4.3", "@react-navigation/native": "^7.0.14", "eas-cli": "^16.7.2", "expo": "~52.0.0", "expo-blur": "~14.0.3", "expo-constants": "~17.0.8", "expo-dev-client": "~5.0.20", "expo-file-system": "~18.0.12", "expo-font": "~13.0.4", "expo-haptics": "~14.0.1", "expo-image": "~2.0.7", "expo-image-manipulator": "~13.0.6", "expo-image-picker": "~16.0.6", "expo-linking": "~7.0.5", "expo-location": "~18.0.10", "expo-media-library": "~17.0.6", "expo-router": "~4.0.21", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.1", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.9", "expo-web-browser": "~14.0.2", "metro": "^0.81.0", "metro-core": "^0.76.8", "metro-runtime": "^0.76.8", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.9", "react-native-gesture-handler": "~2.20.2", "react-native-get-random-values": "^1.11.0", "react-native-google-mobile-ads": "^14.7.2", "react-native-google-places-autocomplete": "^2.5.7", "react-native-maps": "1.18.0", "react-native-paper": "^5.12.3", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-web": "~0.19.6", "react-native-webview": "13.12.5"}, "devDependencies": {"@babel/core": "^7.23.7", "@types/react": "~18.3.12", "@types/react-native": "^0.73.0", "eslint": "^8.56.0", "eslint-config-expo": "~8.0.1", "typescript": "^5.3.0"}, "private": true}