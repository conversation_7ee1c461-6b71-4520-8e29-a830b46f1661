# 软件产品说明：Mepond

## 1. 软件功能介绍

MapPond 是一款基于移动平台的地图应用，旨在为用户提供便捷的地理区域标记和管理功能。用户可以在地图上精确绘制多边形区域，并对这些区域进行个性化管理。

主要功能包括：

*   **地图交互与多边形绘制：**
    *   在交互式地图上通过点击标记顶点，轻松绘制自定义多边形区域。
    *   支持在绘制过程中撤销上一个标记点。
    *   完成绘制后，系统自动为多边形生成一个默认名称和随机颜色，并计算其面积。
*   **多边形管理：**
    *   **列表展示：** 在专门的“多边形”标签页中，以列表形式清晰展示所有已保存的多边形，包括名称、面积（可选公制/英制单位）和颜色标识。
    *   **重命名：** 用户可以为已保存的多边形修改名称，以便更好地识别和组织。
    *   **删除：** 可以删除不再需要的多边形。
    *   **跳转查看：** 从多边形列表中选择任一多边形，可快速跳转到地图界面并自动缩放至该多边形所在位置。
*   **数据分享与导出：**
    *   **单个分享：** 支持将单个多边形的详细信息（包括ID、名称、面积、颜色、创建时间及坐标点）以 JSON 格式通过系统分享功能发送给他人或导出到其他应用。
    *   **批量分享：** 可以一次性将所有已保存的多边形数据以 JSON 数组的形式进行分享或导出。
*   **地图功能增强：**
    *   **地点搜索：** 集成了地点搜索功能，用户可以通过输入关键词或坐标（需用户自行在设置中配置有效的 Google Maps API Key）快速定位到地图上的特定位置。
    *   **地图类型切换：** 提供卫星视图和混合视图两种地图样式，用户可根据偏好自由切换。
*   **个性化设置：**
    *   **单位制切换：** 支持在公制单位（米、公顷）和英制单位（英尺、英亩）之间切换，以适应不同用户的使用习惯。
    *   **API Key 配置：** 用户可以在设置界面输入自己的 Google Maps API Key，以启用地点搜索功能。
    *   **数据重置：** 提供应用数据重置功能，允许用户清除所有已保存的多边形及其他相关数据。
*   **用户体验：**
    *   应用基于 Expo 和 React Native 构建，提供流畅的跨平台用户体验（当前主要针对 Android）。
    *   使用标签页导航，方便用户在地图、多边形列表和设置等主要功能区之间切换。

## 2. 技术栈

本软件主要采用以下技术构建：

*   **核心框架与语言：**
    *   **Expo:** 一个开源平台，用于构建通用的原生 iOS 和 Android 应用，简化了开发、构建和部署流程。
    *   **React Native:** 一个由 Facebook 开发的 JavaScript 框架，用于构建原生移动应用。
    *   **TypeScript:** JavaScript 的一个超集，添加了静态类型检查，增强了代码的可维护性和健壮性。
*   **导航：**
    *   **React Navigation:** 为 React Native 应用提供路由和导航解决方案，本项目使用其底部标签页导航器。
*   **地图与位置服务：**
    *   **`react-native-maps`:** 提供地图组件，支持 Google Maps (通过 `PROVIDER_GOOGLE`)，用于显示地图、绘制标记点、多边形和折线等。
    *   **`expo-location`:** 用于获取设备的地理位置信息。
    *   **`react-native-google-places-autocomplete`:** 集成 Google Places API，实现地点搜索和自动完成功能。
*   **UI 组件与样式：**
    *   **`@expo/vector-icons`:** 提供丰富的矢量图标集。
    *   **`react-native-paper`:** (根据 `package.json` 可能使用) 提供 Material Design 风格的 UI 组件。
    *   自定义 UI 组件 (位于 `components` 目录)。
*   **状态管理与数据持久化：**
    *   **React Context API (`PolygonContext.tsx`):** 用于在应用组件间共享和管理多边形数据、用户偏好设置（如单位制、地图类型）等状态。
    *   **`@react-native-async-storage/async-storage`:** (推测使用，常见于此类应用) 用于在设备本地持久化存储用户数据，如保存的多边形、API Key 等。
*   **构建与依赖管理：**
    *   **npm:** Node.js 包管理器，用于管理项目依赖。
    *   **EAS CLI (`eas-cli`):** Expo Application Services 命令行工具，用于构建和提交应用。
*   **其他辅助库：**
    *   `expo-haptics`: 提供触觉反馈。
    *   `expo-file-system`, `expo-media-library`: (根据 `package.json` 可能用于文件操作，但当前核心功能未明显体现)。
    *   `expo-image-picker`, `expo-image-manipulator`: (根据 `package.json` 可能用于图片处理，但当前核心功能未明显体现)。

## 3. 功能框架图

```mermaid
graph TD
    A[Mepond App] --> B["主导航 (Tabs)"];
    B --> C["地图模块 (MapScreen)"];
    B --> D["多边形管理模块 (PolygonsScreen)"];
    B --> E["设置模块 (SettingsScreen)"];

    subgraph C [地图模块]
        C1[地图展示与交互]
        C2[多边形绘制]
        C3["地点搜索 (需API Key)"]
        C4[查看已存多边形信息]
    end

    subgraph D [多边形管理模块]
        D1[多边形列表展示]
        D2[重命名多边形]
        D3[删除多边形]
        D4[分享单个/所有多边形]
        D5[跳转至地图查看多边形]
    end

    subgraph E [设置模块]
        E1["单位制切换 (公制/英制)"]
        E2["地图类型切换 (卫星/混合)"]
        E3["Google Maps API Key 配置"]
        E4[应用数据重置]
        E5[关于应用信息]
    end

    F["核心数据管理 (PolygonContext)"]
    F --> C;
    F --> D;
    F --> E;

    G[设备功能]
    G -- "位置服务" --> C1;
    G -- "存储" --> F;
    G -- "分享" --> D4;
```

## 4. 核心流程图

### 4.1 绘制新多边形流程

```mermaid
flowchart TD
    A[用户在地图界面] --> B{"点击'开始绘制'按钮?"};
    B -- 是 --> C[进入绘制模式, 界面显示绘制操作按钮];
    C --> D{用户在地图上点击?};
    D -- 是 --> E[添加点击坐标为新顶点];
    E --> F{当前顶点数 < 3?};
    F -- 是 --> D;
    F -- 否(>=3) --> G{用户操作?};
    G -- "点击'完成绘制'" --> H["保存当前多边形数据(ID, 坐标, 默认名, 颜色, 计算面积)"];
    H --> I[退出绘制模式];
    I --> J[在地图上显示新绘制的多边形];
    J --> K[结束];
    G -- "点击 "撤销上一点"" --> L[移除最后一个添加的顶点];
    L --> D;
    G -- "点击 "取消绘制"" --> M[清空当前绘制的顶点];
    M --> I;
    B -- 否 --> A;
```

### 4.2 管理已存多边形流程 (以“多边形列表”界面为例)

```mermaid
flowchart TD
    A["用户进入'多边形列表'界面"] --> B[加载并显示所有已保存的多边形列表];
    B --> C{用户选择一个多边形并操作?};
    C -- "选择(Goto)" --> D[导航到地图界面, 并将视图缩放至该多边形];
    D --> E[结束];
    C -- "选择(Rename)" --> F[弹出重命名对话框, 显示当前名称];
    F --> G{用户输入新名称并确认?};
    G -- 是 --> H[更新该多边形的名称];
    H --> B;
    G -- 否 (取消) --> B;
    C -- "选择(Share)" --> I[将该多边形数据转换为JSON格式];
    I --> J[调用系统分享功能];
    J --> B;
    C -- "选择(Delete)" --> K[弹出删除确认对话框];
    K --> L{用户确认删除?};
    L -- 是 --> M[从存储中删除该多边形];
    M --> B;
    L -- 否 (取消) --> B;
    C -- 无操作/返回 --> E;
    B -- "点击'分享所有'" --> N[将所有多边形数据转换为JSON数组];
    N --> O[调用系统分享功能];
    O --> B;
```