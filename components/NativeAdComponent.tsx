import React from 'react';
import { Platform, Text, View } from 'react-native';
import { BannerAd, BannerAdSize, TestIds } from 'react-native-google-mobile-ads';

// Test ad unit ID for development - replace with real ID for production
const bannerAdUnitID = __DEV__
  ? TestIds.ADAPTIVE_BANNER // Use ADAPTIVE_BANNER for version 14.7.2
  : Platform.OS === 'android'
  ? 'ca-app-pub-8377651725703584/8902514184'
  : 'ca-app-pub-3940256099942544/2247696110'; // Your provided test ID

interface NativeAdComponentProps {
  style?: any;
}

export default function NativeAdComponent({ style }: NativeAdComponentProps) {
  const handleAdFailedToLoad = (error: any) => {
    console.warn('Banner Ad failed to load:', error);
  };

  const handleAdLoaded = () => {
    console.log('Banner Ad loaded successfully');
  };

  return (
    <View style={[styles.container, style]}>
      <View style={styles.adContainer}>
        <View style={styles.adHeader}>
          <Text style={styles.adLabel}>Ad</Text>
        </View>

        <BannerAd
          unitId={bannerAdUnitID}
          size={BannerAdSize.BANNER}
          requestOptions={{
            requestNonPersonalizedAdsOnly: true,
          }}
          onAdLoaded={handleAdLoaded}
          onAdFailedToLoad={handleAdFailedToLoad}
        />
      </View>
    </View>
  );
}

const styles = {
  container: {
    marginVertical: 8,
    marginHorizontal: 8,
    alignItems: 'center' as const, // Center the entire ad container
  },
  adContainer: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 6,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    alignItems: 'center' as const, // Center content within container
    justifyContent: 'center' as const,
    width: '100%' as const,
  },
  adHeader: {
    marginBottom: 8,
    alignSelf: 'flex-start' as const, // Keep "Ad" label aligned to start
  },
  adLabel: {
    fontSize: 10,
    color: '#999',
    fontWeight: 'bold' as const,
    textTransform: 'uppercase' as const,
  },
};